import 'package:flutter/foundation.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Provider for managing package data state across the application
///
/// This provider handles:
/// - Pickup details state
/// - Receiver details state
/// - Package data state
/// - Notifying listeners when data changes
class PackageDataProvider extends ChangeNotifier {
  Map<String, dynamic>? _pickupData;
  Map<String, dynamic>? _receiverData;
  Map<String, dynamic>? _packageData;
  bool _isLoading = false;
  String? _error;

  /// Get pickup data
  Map<String, dynamic>? get pickupData => _pickupData;

  /// Get receiver data
  Map<String, dynamic>? get receiverData => _receiverData;

  /// Get package data
  Map<String, dynamic>? get packageData => _packageData;

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Get current error message
  String? get error => _error;

  /// Check if pickup data exists
  bool get hasPickupData => _pickupData != null && _pickupData!.isNotEmpty;

  /// Check if receiver data exists
  bool get hasReceiverData => _receiverData != null && _receiverData!.isNotEmpty;

  /// Check if package data exists
  bool get hasPackageData => _packageData != null && _packageData!.isNotEmpty;

  /// Load all package data from storage
  Future<void> loadAllData() async {
    try {
      _setLoading(true);
      _clearError();

      final pickup = await PackageDataService.getPickupData();
      final receiver = await PackageDataService.getReceiverData();
      final package = await PackageDataService.getPackageData();

      _pickupData = pickup;
      _receiverData = receiver;
      _packageData = package;

      notifyListeners();
    } catch (e) {
      _setError('Failed to load package data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update pickup data
  Future<void> updatePickupData(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _clearError();

      await PackageDataService.savePickupData(data);
      _pickupData = data;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to save pickup data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update receiver data
  Future<void> updateReceiverData(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _clearError();

      await PackageDataService.saveReceiverData(data);
      _receiverData = data;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to save receiver data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update package data
  Future<void> updatePackageData(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _clearError();

      await PackageDataService.savePackageData(data);
      _packageData = data;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to save package data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Clear all package data
  Future<void> clearAllData() async {
    try {
      _setLoading(true);
      _clearError();

      await PackageDataService.clearAllData();
      _pickupData = null;
      _receiverData = null;
      _packageData = null;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear package data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh pickup data from storage
  Future<void> refreshPickupData() async {
    try {
      final pickup = await PackageDataService.getPickupData();
      _pickupData = pickup;
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh pickup data: $e');
    }
  }

  /// Refresh receiver data from storage
  Future<void> refreshReceiverData() async {
    try {
      final receiver = await PackageDataService.getReceiverData();
      _receiverData = receiver;
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh receiver data: $e');
    }
  }

  /// Refresh package data from storage
  Future<void> refreshPackageData() async {
    try {
      final package = await PackageDataService.getPackageData();
      _packageData = package;
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh package data: $e');
    }
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Helper method to set error
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Helper method to clear error
  void _clearError() {
    _error = null;
  }

  /// Get formatted pickup address for display
  String get formattedPickupAddress {
    if (_pickupData == null) return '';
    
    final address = _pickupData!['fullAddress'] ?? '';
    final landmark = _pickupData!['landmark'] ?? '';
    final state = _pickupData!['state'] ?? '';
    
    String formatted = address;
    if (landmark.isNotEmpty) {
      formatted += ', $landmark';
    }
    if (state.isNotEmpty) {
      formatted += ', $state';
    }
    
    return formatted;
  }

  /// Get formatted receiver address for display
  String get formattedReceiverAddress {
    if (_receiverData == null) return '';
    
    final address = _receiverData!['address'] ?? '';
    final state = _receiverData!['state'] ?? '';
    
    String formatted = address;
    if (state.isNotEmpty) {
      formatted += ', $state';
    }
    
    return formatted;
  }

  /// Get pickup contact info for display
  String get pickupContactInfo {
    if (_pickupData == null) return '';
    
    final name = _pickupData!['senderName'] ?? '';
    final phone = _pickupData!['phone'] ?? '';
    
    if (name.isNotEmpty && phone.isNotEmpty) {
      return '$name • $phone';
    } else if (name.isNotEmpty) {
      return name;
    } else if (phone.isNotEmpty) {
      return phone;
    }
    
    return '';
  }

  /// Get receiver contact info for display
  String get receiverContactInfo {
    if (_receiverData == null) return '';
    
    final name = _receiverData!['name'] ?? '';
    final phone = _receiverData!['phone'] ?? '';
    
    if (name.isNotEmpty && phone.isNotEmpty) {
      return '$name • $phone';
    } else if (name.isNotEmpty) {
      return name;
    } else if (phone.isNotEmpty) {
      return phone;
    }
    
    return '';
  }
}

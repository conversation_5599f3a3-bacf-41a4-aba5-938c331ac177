import 'dart:convert';
import 'package:rideoon/services/order_api_service.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/order/collection.dart';
import 'package:rideoon/models/order/package.dart';
import 'package:rideoon/models/address/address_request.dart';
import 'package:rideoon/models/api_response.dart';

/// Service for managing package delivery data using API calls
/// This service replaces local storage with API-based data management
class PackageDataService {
  // Current draft order UUID for temporary data storage
  static String? _currentDraftOrderUuid;

  /// Save sender details by creating/updating pickup address
  static Future<String?> saveSenderData(Map<String, dynamic> data) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      // Create address request from sender data
      final phoneStr = data['phone']?.toString() ?? '';
      final phoneNumber = int.tryParse(phoneStr.replaceAll(RegExp(r'[^\d]'), '')) ?? 0;

      final addressRequest = AddressRequest(
        name: data['senderName'] ?? '',
        phoneNumber: phoneNumber,
        street: '${data['fullAddress'] ?? ''}${data['landmark'] != null ? ', ${data['landmark']}' : ''}',
        city: data['city'] ?? '',
        state: data['state'] ?? '',
        country: 'Nigeria',
        longitude: data['longitude'] is num ? (data['longitude'] as num).toDouble() : null,
        latitude: data['latitude'] is num ? (data['latitude'] as num).toDouble() : null,
        type: 'pickup',
      );

      final response = await AddressService.createAddress(addressRequest, authToken: authToken);
      if (response.success && response.data != null) {
        return response.data!.uuid;
      }
      return null;
    } catch (e) {
      print('Error saving sender data: $e');
      return null;
    }
  }

  /// Get sender details from user's pickup addresses
  static Future<Map<String, dynamic>?> getSenderData() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final addresses = await AddressService.getSavedAddresses(authToken: authToken);
      // Since API returns all addresses with type 'address', use the first address for pickup
      final pickupAddresses = addresses.where((addr) =>
        addr['type'] == 'pickup' || addr['type'] == 'address'
      ).toList();

      if (pickupAddresses.isNotEmpty) {
        // Use the first address for pickup data
        final latest = pickupAddresses.first;
        return {
          'senderName': latest['name'],
          'phone': latest['phoneNumber'],
          'fullAddress': latest['street'],
          'landmark': latest['landmark'],
          'city': latest['city'],
          'lga': latest['localGovernmentArea'],
          'state': latest['state'],
          'latitude': double.tryParse(latest['latitude'] ?? ''),
          'longitude': double.tryParse(latest['longitude'] ?? ''),
          'addressUuid': latest['uuid'],
        };
      }
      return null;
    } catch (e) {
      print('Error getting sender data: $e');
      return null;
    }
  }

  /// Save pickup details - same as sender data
  static Future<String?> savePickupData(Map<String, dynamic> data) async {
    return await saveSenderData(data);
  }

  /// Get pickup details - same as sender data
  static Future<Map<String, dynamic>?> getPickupData() async {
    return await getSenderData();
  }

  /// Save receiver details by creating/updating delivery address
  static Future<String?> saveReceiverData(Map<String, dynamic> data) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      // Create address request from receiver data
      final phoneStr = data['phone']?.toString() ?? '';
      final phoneNumber = int.tryParse(phoneStr.replaceAll(RegExp(r'[^\d]'), '')) ?? 0;

      final addressRequest = AddressRequest(
        name: data['name'] ?? '',
        phoneNumber: phoneNumber,
        street: '${data['address'] ?? ''}${data['landmark'] != null ? ', ${data['landmark']}' : ''}',
        city: data['city'] ?? '',
        state: data['state'] ?? '',
        country: 'Nigeria',
        longitude: data['longitude'] is num ? (data['longitude'] as num).toDouble() : null,
        latitude: data['latitude'] is num ? (data['latitude'] as num).toDouble() : null,
        type: 'delivery',
      );

      final response = await AddressService.createAddress(addressRequest, authToken: authToken);
      if (response.success && response.data != null) {
        return response.data!.uuid;
      }
      return null;
    } catch (e) {
      print('Error saving receiver data: $e');
      return null;
    }
  }

  /// Get receiver details from user's delivery addresses
  static Future<Map<String, dynamic>?> getReceiverData() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final addresses = await AddressService.getSavedAddresses(authToken: authToken);
      // Since API returns all addresses with type 'address', get the most recent one
      // that was likely used for delivery (we'll use the second address if available, or first)
      final deliveryAddresses = addresses.where((addr) =>
        addr['type'] == 'delivery' || addr['type'] == 'address'
      ).toList();

      if (deliveryAddresses.isNotEmpty) {
        final latest = deliveryAddresses.first;
        return {
          'name': latest['name'],
          'phone': latest['phoneNumber'],
          'address': latest['street'],
          'city': latest['city'],
          'state': latest['state'],
          'latitude': double.tryParse(latest['latitude'] ?? ''),
          'longitude': double.tryParse(latest['longitude'] ?? ''),
          'addressUuid': latest['uuid'],
        };
      }
      return null;
    } catch (e) {
      print('Error getting receiver data: $e');
      return null;
    }
  }

  /// Save package details to current draft order
  static Future<bool> savePackageData(Map<String, dynamic> data) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // If no current draft order, create one
      if (_currentDraftOrderUuid == null) {
        final createRequest = CreateCollectionRequest(
          orderPickupType: data['deliveryType'] ?? 'instant',
          orderPickupDate: data['pickupDate'] != null ? DateTime.parse(data['pickupDate']) : null,
          note: 'Draft order for package data',
        );

        final response = await OrderApiService.createOrder(createRequest, authToken: authToken);
        if (response.success && response.data != null) {
          _currentDraftOrderUuid = response.data!.uuid;
        } else {
          return false;
        }
      }

      // Package data is now managed through the order system
      return true;
    } catch (e) {
      print('Error saving package data: $e');
      return false;
    }
  }

  /// Get package details from current draft order
  static Future<Map<String, dynamic>?> getPackageData() async {
    try {
      if (_currentDraftOrderUuid == null) return null;

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.getOrder(_currentDraftOrderUuid!, authToken: authToken);
      if (response.success && response.data != null) {
        final order = response.data!;
        return {
          'deliveryType': order.orderPickupType,
          'pickupDate': order.orderPickupDate?.toIso8601String(),
          'note': order.note,
          'orderUuid': order.uuid,
        };
      }
      return null;
    } catch (e) {
      print('Error getting package data: $e');
      return null;
    }
  }

  /// Clear all package data by clearing current draft order
  static Future<void> clearAllData() async {
    _currentDraftOrderUuid = null;
    // Note: We don't delete the draft order from API as it might be needed later
  }

  /// Save completed order/shipment - now handled by API
  static Future<bool> saveCompletedOrder(Map<String, dynamic> orderData) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Convert local order data to API format
      final createRequest = CreateCollectionRequest(
        orderPickupType: orderData['deliveryType'] ?? 'instant',
        orderPickupDate: orderData['orderDate'] != null ? DateTime.parse(orderData['orderDate']) : null,
        note: orderData['note'] ?? 'Completed order',
        pickupAddressUuid: orderData['pickupAddressUuid'],
        deliveryAddressUuid: orderData['deliveryAddressUuid'],
      );

      final response = await OrderApiService.createOrder(createRequest, authToken: authToken);
      return response.success;
    } catch (e) {
      print('Error saving completed order: $e');
      return false;
    }
  }

  /// Get completed orders/shipments from API
  static Future<List<Map<String, dynamic>>> getCompletedOrders() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final response = await OrderApiService.getOrders(
        queryParams: {'status': 'completed'},
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data!.map((order) => {
          'id': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
          'timestamp': order.created.millisecondsSinceEpoch,
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting completed orders: $e');
      return [];
    }
  }

  /// Save current shipment - now handled by API
  static Future<bool> saveCurrentShipment(Map<String, dynamic> shipmentData) async {
    // Current shipments are now managed through the orders API
    return await saveCompletedOrder(shipmentData);
  }

  /// Get current shipments from API
  static Future<List<Map<String, dynamic>>> getCurrentShipments() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final response = await OrderApiService.getOrders(
        queryParams: {'status': 'pending,confirmed,in_transit'},
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data!.map((order) => {
          'id': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
          'timestamp': order.created.millisecondsSinceEpoch,
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting current shipments: $e');
      return [];
    }
  }

  /// Update shipment status via API
  static Future<bool> updateShipmentStatus(String shipmentId, String newStatus) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Update order status through API
      final updateRequest = UpdateCollectionRequest(
        // Note: The API might not have a direct status field,
        // this would need to be implemented based on actual API schema
        note: 'Status updated to: $newStatus',
      );

      final response = await OrderApiService.updateOrder(shipmentId, updateRequest, authToken: authToken);
      return response.success;
    } catch (e) {
      print('Error updating shipment status: $e');
      return false;
    }
  }

  /// Clear completed orders - API managed, no local clearing needed
  static Future<void> clearCompletedOrders() async {
    // Orders are managed by API, no local clearing needed
    print('Clear completed orders: API managed, no action needed');
  }

  /// Clear current shipments - API managed, no local clearing needed
  static Future<void> clearCurrentShipments() async {
    // Shipments are managed by API, no local clearing needed
    print('Clear current shipments: API managed, no action needed');
  }

  /// Check if pickup details exist
  static Future<bool> hasPickupData() async {
    final data = await getPickupData();
    return data != null && data.isNotEmpty;
  }

  /// Check if receiver details exist
  static Future<bool> hasReceiverData() async {
    final data = await getReceiverData();
    return data != null && data.isNotEmpty;
  }

  /// Check if sender details exist
  static Future<bool> hasSenderData() async {
    final data = await getSenderData();
    return data != null && data.isNotEmpty;
  }

  /// Check if package details exist
  static Future<bool> hasPackageData() async {
    final data = await getPackageData();
    return data != null && data.isNotEmpty;
  }

  /// Save pickup location to history - now uses address API
  static Future<void> savePickupLocationToHistory(Map<String, dynamic> locationData) async {
    // Location history is now managed through saved addresses
    await saveSenderData(locationData);
  }

  /// Get pickup location history from saved addresses
  static Future<List<Map<String, dynamic>>> getPickupLocationHistory() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final addresses = await AddressService.getSavedAddresses(authToken: authToken);
      final pickupAddresses = addresses.where((addr) => addr['type'] == 'pickup').toList();

      return pickupAddresses.map((addr) => {
        'address': addr['street'],
        'name': addr['name'],
        'phone': addr['phoneNumber'],
        'city': addr['city'],
        'state': addr['state'],
        'latitude': double.tryParse(addr['latitude'] ?? ''),
        'longitude': double.tryParse(addr['longitude'] ?? ''),
        'timestamp': DateTime.parse(addr['created'] ?? DateTime.now().toIso8601String()).millisecondsSinceEpoch,
        'addressUuid': addr['uuid'],
      }).toList();
    } catch (e) {
      print('Error getting pickup location history: $e');
      return [];
    }
  }

  /// Save receiver location to history - now uses address API
  static Future<void> saveReceiverLocationToHistory(Map<String, dynamic> locationData) async {
    // Location history is now managed through saved addresses
    await saveReceiverData(locationData);
  }

  /// Get receiver location history from saved addresses
  static Future<List<Map<String, dynamic>>> getReceiverLocationHistory() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final addresses = await AddressService.getSavedAddresses(authToken: authToken);
      final deliveryAddresses = addresses.where((addr) => addr['type'] == 'delivery').toList();

      return deliveryAddresses.map((addr) => {
        'address': addr['street'],
        'name': addr['name'],
        'phone': addr['phoneNumber'],
        'city': addr['city'],
        'state': addr['state'],
        'latitude': double.tryParse(addr['latitude'] ?? ''),
        'longitude': double.tryParse(addr['longitude'] ?? ''),
        'timestamp': DateTime.parse(addr['created'] ?? DateTime.now().toIso8601String()).millisecondsSinceEpoch,
        'addressUuid': addr['uuid'],
      }).toList();
    } catch (e) {
      print('Error getting receiver location history: $e');
      return [];
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/address/address.dart';
import 'package:rideoon/models/address/address_request.dart';
import 'package:rideoon/models/api_response.dart';

/// Provider for managing address state across the application
///
/// This provider handles:
/// - Loading saved addresses
/// - Creating new addresses
/// - Updating existing addresses
/// - Deleting addresses
/// - Notifying listeners when address data changes
class AddressProvider extends ChangeNotifier {
  List<Map<String, dynamic>> _addresses = [];
  bool _isLoading = false;
  String? _error;

  /// Get all addresses
  List<Map<String, dynamic>> get addresses => List.unmodifiable(_addresses);

  /// Get addresses filtered by type
  List<Map<String, dynamic>> getAddressesByType(String type) {
    // If no addresses have specific types, return all addresses
    final hasTypedAddresses = _addresses.any((addr) =>
      addr['type'] != null &&
      addr['type'].toString().isNotEmpty &&
      addr['type'] != 'address'
    );

    if (!hasTypedAddresses) {
      // Return all addresses if none have specific types
      return List.from(_addresses);
    }

    // Filter by type, including fallback cases
    return _addresses.where((addr) {
      final addressType = addr['type']?.toString().toLowerCase().trim();

      if (addressType == null || addressType.isEmpty || addressType == 'address') {
        return true; // Include addresses without specific types
      }

      // Exact match
      if (addressType == type.toLowerCase()) {
        return true;
      }

      // Handle type variations
      if (type.toLowerCase() == 'receiver') {
        return addressType == 'delivery' || addressType == 'deliver';
      }

      return false;
    }).toList();
  }

  /// Get pickup addresses
  List<Map<String, dynamic>> get pickupAddresses => getAddressesByType('pickup');

  /// Get receiver addresses
  List<Map<String, dynamic>> get receiverAddresses => getAddressesByType('receiver');

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Get current error message
  String? get error => _error;

  /// Load all saved addresses from the API
  Future<void> loadAddresses() async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken != null) {
        final addresses = await AddressService.getSavedAddresses(authToken: authToken);

        _addresses = addresses.map((addr) => {
          ...addr,
          'icon': _getAddressIcon(addr['type']),
          'title': _getAddressTitle(addr),
        }).toList();
      } else {
        _addresses = [];
      }
    } catch (e) {
      print('AddressProvider: Error loading addresses: $e');
      _setError('Failed to load addresses: $e');
      _addresses = [];
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new address
  Future<bool> createAddress(AddressRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to save address');
        return false;
      }

      final response = await AddressService.createAddress(request, authToken: authToken);
      
      if (response.success) {
        // Reload addresses to get the updated list
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to create address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to create address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing address
  Future<bool> updateAddress(String addressUuid, AddressRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to update address');
        return false;
      }

      final response = await AddressService.updateAddress(addressUuid, request, authToken: authToken);
      
      if (response.success) {
        // Reload addresses to get the updated list
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to update address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to update address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an address
  Future<bool> deleteAddress(String addressUuid) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to delete address');
        return false;
      }

      final response = await AddressService.deleteAddress(addressUuid, authToken: authToken);
      
      if (response.success) {
        // Remove the address from local list immediately for better UX
        _addresses.removeWhere((addr) => addr['id'] == addressUuid);
        notifyListeners();
        
        // Then reload to ensure consistency
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to delete address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to delete address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh addresses (alias for loadAddresses for clarity)
  Future<void> refresh() async {
    await loadAddresses();
  }

  /// Clear all addresses (for logout scenarios)
  void clearAddresses() {
    _addresses.clear();
    _clearError();
    notifyListeners();
  }



  /// Helper method to set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Helper method to set error
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Helper method to clear error
  void _clearError() {
    _error = null;
  }

  /// Get icon for address type
  String _getAddressIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'pickup':
        return 'pickup';
      case 'receiver':
      case 'delivery':
        return 'delivery';
      case 'home':
        return 'home';
      case 'work':
      case 'office':
        return 'work';
      default:
        return 'location';
    }
  }

  /// Get title for address
  String _getAddressTitle(Map<String, dynamic> address) {
    final name = address['name'] ?? address['addressName'];
    final street = address['street'] ?? address['fullAddress'] ?? address['address'];

    if (name != null && name.toString().isNotEmpty) {
      return name.toString();
    } else if (street != null && street.toString().isNotEmpty) {
      final streetStr = street.toString();
      // Return first 30 characters of street address
      return streetStr.length > 30 ? '${streetStr.substring(0, 30)}...' : streetStr;
    }

    return 'Saved Address';
  }

  // ============================================================================
  // ORDER AND PACKAGE ADDRESS MANAGEMENT METHODS
  // ============================================================================

  /// Fetch addresses for a specific order
  ///
  /// [orderUuid] - The order UUID to fetch addresses for
  /// [addressType] - Optional address type ('pickup' or 'delivery')
  /// Returns a map with 'pickup' and 'delivery' keys containing Address objects or null
  Future<Map<String, Address?>?> getOrderAddresses(
    String orderUuid, {
    String? addressType,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to fetch order addresses');
        return null;
      }

      final response = await AddressService.getOrderAddresses(
        orderUuid,
        addressType: addressType,
        authToken: authToken,
      );

      if (response.success) {
        return response.data;
      } else {
        _setError('Failed to fetch order addresses: ${response.message}');
        return null;
      }
    } catch (e) {
      _setError('Failed to fetch order addresses: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Fetch addresses for a specific package
  ///
  /// [orderUuid] - The order UUID
  /// [packageUuid] - The package UUID to fetch addresses for
  /// [addressType] - Optional address type ('pickup' or 'delivery')
  /// Returns a map with 'pickup' and 'delivery' keys containing Address objects or null
  Future<Map<String, Address?>?> getPackageAddresses(
    String orderUuid,
    String packageUuid, {
    String? addressType,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to fetch package addresses');
        return null;
      }

      final response = await AddressService.getPackageAddresses(
        orderUuid,
        packageUuid,
        addressType: addressType,
        authToken: authToken,
      );

      if (response.success) {
        return response.data;
      } else {
        _setError('Failed to fetch package addresses: ${response.message}');
        return null;
      }
    } catch (e) {
      _setError('Failed to fetch package addresses: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an address on an order
  ///
  /// [orderUuid] - The order UUID
  /// [addressUuid] - The address UUID to update
  /// [request] - The address update request
  /// Returns true if successful, false otherwise
  Future<bool> updateOrderAddress(
    String orderUuid,
    String addressUuid,
    AddressRequest request,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to update order address');
        return false;
      }

      final response = await AddressService.updateOrderAddress(
        orderUuid,
        addressUuid,
        request,
        authToken: authToken,
      );

      if (response.success) {
        // Optionally refresh the general address list if needed
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to update order address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to update order address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an address from an order
  ///
  /// [orderUuid] - The order UUID
  /// [addressUuid] - The address UUID to delete
  /// Returns true if successful, false otherwise
  Future<bool> deleteOrderAddress(
    String orderUuid,
    String addressUuid,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to delete order address');
        return false;
      }

      final response = await AddressService.deleteOrderAddress(
        orderUuid,
        addressUuid,
        authToken: authToken,
      );

      if (response.success) {
        // Optionally refresh the general address list if needed
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to delete order address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to delete order address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an address on a package
  ///
  /// [orderUuid] - The order UUID
  /// [packageUuid] - The package UUID
  /// [addressUuid] - The address UUID to update
  /// [request] - The address update request
  /// Returns true if successful, false otherwise
  Future<bool> updatePackageAddress(
    String orderUuid,
    String packageUuid,
    String addressUuid,
    AddressRequest request,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to update package address');
        return false;
      }

      final response = await AddressService.updatePackageAddress(
        orderUuid,
        packageUuid,
        addressUuid,
        request,
        authToken: authToken,
      );

      if (response.success) {
        // Optionally refresh the general address list if needed
        await loadAddresses();
        return true;
      } else {
        _setError('Failed to update package address: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to update package address: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Convenience method to get only pickup address for an order
  Future<Address?> getOrderPickupAddress(String orderUuid) async {
    final addresses = await getOrderAddresses(orderUuid, addressType: 'pickup');
    return addresses?['pickup'];
  }

  /// Convenience method to get only delivery address for an order
  Future<Address?> getOrderDeliveryAddress(String orderUuid) async {
    final addresses = await getOrderAddresses(orderUuid, addressType: 'delivery');
    return addresses?['delivery'];
  }

  /// Convenience method to get only pickup address for a package
  Future<Address?> getPackagePickupAddress(String orderUuid, String packageUuid) async {
    final addresses = await getPackageAddresses(orderUuid, packageUuid, addressType: 'pickup');
    return addresses?['pickup'];
  }

  /// Convenience method to get only delivery address for a package
  Future<Address?> getPackageDeliveryAddress(String orderUuid, String packageUuid) async {
    final addresses = await getPackageAddresses(orderUuid, packageUuid, addressType: 'delivery');
    return addresses?['delivery'];
  }

  /// Check if an order has both pickup and delivery addresses
  Future<bool> orderHasCompleteAddresses(String orderUuid) async {
    final addresses = await getOrderAddresses(orderUuid);
    return addresses != null &&
           addresses['pickup'] != null &&
           addresses['delivery'] != null;
  }

  /// Check if a package has both pickup and delivery addresses
  Future<bool> packageHasCompleteAddresses(String orderUuid, String packageUuid) async {
    final addresses = await getPackageAddresses(orderUuid, packageUuid);
    return addresses != null &&
           addresses['pickup'] != null &&
           addresses['delivery'] != null;
  }
}

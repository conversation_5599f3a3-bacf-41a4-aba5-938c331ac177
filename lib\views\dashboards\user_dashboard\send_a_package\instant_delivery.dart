import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/providers/order_provider.dart';
import 'package:rideoon/providers/package_provider.dart';
import 'package:rideoon/providers/address_provider.dart';

/// Instant Delivery form widget
///
/// This widget contains the form fields specific to instant delivery.
/// It's designed to be used within a tabbed interface.
class InstantDeliveryWidget extends StatefulWidget {
  final VoidCallback? onAddToCargo;
  final VoidCallback? onAddPickupDetails;
  final VoidCallback? onAddReceiverDetails;
  final Function(Map<String, dynamic>)? onDataChanged;
  final bool hasPickupDetails;
  final bool hasReceiverDetails;

  const InstantDeliveryWidget({
    super.key,
    this.onAddToCargo,
    this.onAddPickupDetails,
    this.onAddReceiverDetails,
    this.onDataChanged,
    this.hasPickupDetails = false,
    this.hasReceiverDetails = false,
  });

  @override
  State<InstantDeliveryWidget> createState() => _InstantDeliveryWidgetState();
}

class _InstantDeliveryWidgetState extends State<InstantDeliveryWidget> {
  bool _hasPickupData = false;
  bool _hasReceiverData = false;

  // Data for preview
  Map<String, dynamic>? _pickupData;
  Map<String, dynamic>? _receiverData;

  @override
  void initState() {
    super.initState();
    _checkSavedData();
  }

  @override
  void didUpdateWidget(InstantDeliveryWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Refresh data when parent state changes
    if (oldWidget.hasPickupDetails != widget.hasPickupDetails ||
        oldWidget.hasReceiverDetails != widget.hasReceiverDetails) {
      _checkSavedData();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Check if saved data exists for pickup and receiver details
  Future<void> _checkSavedData() async {
    final hasPickup = await PackageDataService.hasPickupData();
    final hasReceiver = await PackageDataService.hasReceiverData();

    // Get the actual data for preview
    Map<String, dynamic>? pickupData;
    Map<String, dynamic>? receiverData;

    if (hasPickup) {
      pickupData = await PackageDataService.getPickupData();
    }

    if (hasReceiver) {
      receiverData = await PackageDataService.getReceiverData();
    }

    setState(() {
      _hasPickupData = hasPickup;
      _hasReceiverData = hasReceiver;
      _pickupData = pickupData;
      _receiverData = receiverData;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20), // Increased padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 32), // Increased spacing

            // Pickup Details Section
            _buildPickupDetailsSection(context),

            SizedBox(height: 32), // Increased spacing

            // Receiver Details Section
            _buildReceiverDetailsSection(context),

            SizedBox(height: 50), // Increased bottom spacing
          ],
        ),
      ),
    );
  }







  Widget _buildPickupDetailsSection(BuildContext context) {
    // Use widget parameter for hasData, but show preview if we have internal data
    final hasData = widget.hasPickupDetails || _hasPickupData;
    return _buildDetailsSection(
      context,
      title: hasData ? 'Edit pickup details' : 'Add Pickup details',
      hasData: hasData,
      previewData: _pickupData,
      onAddPressed: () async {
        if (widget.onAddPickupDetails != null) {
          widget.onAddPickupDetails!();
          // Refresh data status after returning from pickup details
          await _checkSavedData();
        }
      },
    );
  }

  Widget _buildReceiverDetailsSection(BuildContext context) {
    // Use widget parameter for hasData, but show preview if we have internal data
    final hasData = widget.hasReceiverDetails || _hasReceiverData;
    return _buildDetailsSection(
      context,
      title: hasData ? 'Edit receiver details' : 'Add receiver details',
      hasData: hasData,
      previewData: _receiverData,
      onAddPressed: () async {
        if (widget.onAddReceiverDetails != null) {
          widget.onAddReceiverDetails!();
          // Refresh data status after returning from receiver details
          await _checkSavedData();
        }
      },
    );
  }

  Widget _buildDetailsSection(
    BuildContext context, {
    required String title,
    required VoidCallback onAddPressed,
    required bool hasData,
    Map<String, dynamic>? previewData,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24), // Increased padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20), // Increased border radius
        border: Border.all(
          color: const Color(0x1A1E1E1E), // Slightly more visible border
          width: 1.5, // Thicker border
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08), // More prominent shadow
            blurRadius: 12, // Increased blur
            offset: const Offset(0, 4), // Increased offset
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: const Color(0xE6000000), // Darker text
                    fontSize: 16, // Increased font size
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600, // Bolder weight
                    height: 1.4,
                  ),
                ),
              ),
              const SizedBox(width: 16), // Increased spacing
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onAddPressed,
                  borderRadius: BorderRadius.circular(16), // Increased border radius
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 14), // Increased padding
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(16), // Increased border radius
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withValues(alpha: 0.4), // More prominent shadow
                          blurRadius: 12, // Increased blur
                          offset: const Offset(0, 4), // Increased offset
                        ),
                      ],
                    ),
                    child: Text(
                      hasData ? 'Edit' : 'Add',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14, // Increased font size
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600, // Bolder weight
                        height: 1.2,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),

          // Preview data when available
          if (hasData && previewData != null) ...[
            const SizedBox(height: 16), // Increased spacing
            _buildPreviewData(previewData),
          ],
        ],
      ),
    );
  }

  Widget _buildPreviewData(Map<String, dynamic> data) {
    return Container(
      padding: const EdgeInsets.all(16), // Increased padding
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.08), // Slightly more visible background
        borderRadius: BorderRadius.circular(12), // Increased border radius
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.15), // More visible border
          width: 1.5, // Thicker border
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (data['name'] != null || data['senderName'] != null) ...[
            _buildPreviewItem(
              'Name',
              data['name'] ?? data['senderName'] ?? '',
              Icons.person_outline,
            ),
          ],
          if (data['state'] != null) ...[
            const SizedBox(height: 12), // Increased spacing
            _buildPreviewItem(
              'State',
              data['state'] ?? '',
              Icons.location_city_outlined,
            ),
          ],
          if (data['address'] != null || data['fullAddress'] != null) ...[
            const SizedBox(height: 12), // Increased spacing
            _buildPreviewItem(
              'Location',
              data['address'] ?? data['fullAddress'] ?? '',
              Icons.location_on_outlined,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPreviewItem(String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20, // Increased icon size
          color: AppColors.primary.withValues(alpha: 0.8), // More visible icon
        ),
        const SizedBox(width: 12), // Increased spacing
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 13, // Increased font size
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600, // Bolder weight
                  color: AppColors.primary.withValues(alpha: 0.9), // More visible
                ),
              ),
              const SizedBox(height: 4), // Increased spacing
              Text(
                value,
                style: TextStyle(
                  fontSize: 14, // Increased font size
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500, // Slightly bolder
                  color: Colors.black.withValues(alpha: 0.85), // Darker text
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Notify parent widget about data changes
  void _notifyDataChanged() {
    if (widget.onDataChanged != null) {
      final data = {
        'itemName': 'Package Item',
        'category': 'General', // Default category
        'itemType': 'Package',
        'deliveryType': 'instant',
        'hasPickupData': _hasPickupData,
        'hasReceiverData': _hasReceiverData,
        'pickupData': _pickupData,
        'receiverData': _receiverData,
      };
      widget.onDataChanged!(data);
    }
  }
}
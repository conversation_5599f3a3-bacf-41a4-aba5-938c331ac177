import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:rideoon/services/api_service.dart';
import 'package:rideoon/services/config_service.dart';
import 'package:rideoon/models/api_response.dart';
import 'package:rideoon/models/address/address.dart';
import 'package:rideoon/models/address/address_request.dart';

/// Service class for handling client address management API calls
///
/// This service provides methods for:
/// - Creating new addresses
/// - Viewing all addresses with pagination and filtering
/// - Viewing a single address by UUID
/// - Updating existing addresses
/// - Deleting addresses
class AddressService {
  /// Create a new address
  ///
  /// Takes an [AddressRequest] and returns an [ApiResponse<Address>]
  /// The API endpoint is /v1/auth/client/account/address/create
  static Future<ApiResponse<Address>> createAddress(AddressRequest request, {String? authToken}) async {
    // Validate request
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      return ApiResponse.error(
        message: 'Validation failed: ${validationErrors.join(', ')}',
        statusCode: 400,
      );
    }

    return await ApiService.makeRequest<Address>(
      endpoint: '/v1/auth/client/account/address/create',
      method: 'POST',
      body: request.toJson(),
      requiresAuth: true,
      authToken: authToken,
      fromJson: (json) => Address.fromJson(json),
    );
  }

  /// View all addresses for the authenticated client
  ///
  /// Returns an [ApiResponse<List<Address>>] with pagination support
  /// The API endpoint is /v1/auth/client/account/address/all
  static Future<ApiResponse<List<Address>>> getAllAddresses({
    String? filter,
    String? sort,
    int? offset,
    int? limit,
    String? authToken,
  }) async {
    try {
      final queryParams = <String, String>{};

      if (filter != null && filter.isNotEmpty) {
        queryParams['filter'] = filter;
      }

      if (sort != null && sort.isNotEmpty) {
        queryParams['sort'] = sort;
      }

      if (offset != null) {
        queryParams['offset'] = offset.toString();
      }

      if (limit != null) {
        queryParams['limit'] = limit.toString();
      }

      // Build URI with query parameters
      Uri uri = Uri.parse('https://riiideon-app.azurewebsites.net/api/v1/auth/client/account/address/all');
      if (queryParams.isNotEmpty) {
        uri = uri.replace(queryParameters: queryParams);
      }

      // Build headers
      final headers = <String, String>{
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'RideOn/1.0.0',
        'x-request-referral': 'riideon',
      };

      if (authToken != null) {
        headers['Authorization'] = 'Bearer $authToken';
      }

      // Make HTTP request
      final response = await http.get(uri, headers: headers);

      if (ConfigService.enableDebugMode) {
        print('AddressService: Making GET request to $uri');
        print('AddressService: Headers: $headers');
        print('AddressService: Response status: ${response.statusCode}');
        print('AddressService: Response body: ${response.body}');
      }

      // Parse response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;

        if (responseData.containsKey('data') && responseData['data'] is List) {
          final addressList = responseData['data'] as List;
          final addresses = addressList.map((addressJson) =>
            Address.fromJson(addressJson as Map<String, dynamic>)
          ).toList();

          return ApiResponse.success(
            message: responseData['message'] as String? ?? 'Success',
            data: addresses,
            statusCode: response.statusCode,
          );
        } else {
          return ApiResponse.success(
            message: responseData['message'] as String? ?? 'Success',
            data: <Address>[],
            statusCode: response.statusCode,
          );
        }
      } else {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;
        return ApiResponse.error(
          message: responseData['message'] as String? ?? 'An error occurred',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get addresses: $e',
        statusCode: 500,
      );
    }
  }

  /// Delete an existing address
  ///
  /// Takes an [addressUuid] and returns an [ApiResponse<bool>]
  /// The API endpoint is /v1/auth/client/account/address/{addressUuid}
  /// Note: This action is irreversible and will permanently delete the address
  static Future<ApiResponse<bool>> deleteAddress(String addressUuid, {String? authToken}) async {
    if (addressUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Address UUID is required',
        statusCode: 400,
      );
    }

    return await ApiService.makeRequest<bool>(
      endpoint: '/v1/auth/client/account/address/$addressUuid',
      method: 'DELETE',
      requiresAuth: true,
      authToken: authToken,
      fromJson: (json) => true, // Delete operations typically return success status
    );
  }

  /// View a single address by UUID
  ///
  /// Takes an [addressUuid] and returns an [ApiResponse<Address>]
  /// The API endpoint is /v1/auth/client/account/address/{addressUuid}
  static Future<ApiResponse<Address>> getAddress(String addressUuid, {String? authToken}) async {
    if (addressUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Address UUID is required',
        statusCode: 400,
      );
    }

    return await ApiService.makeRequest<Address>(
      endpoint: '/v1/auth/client/account/address/$addressUuid',
      method: 'GET',
      requiresAuth: true,
      authToken: authToken,
      fromJson: (json) => Address.fromJson(json),
    );
  }

  /// Update an existing address
  ///
  /// Takes an [addressUuid] and [AddressRequest] and returns an [ApiResponse<Address>]
  /// The API endpoint is /v1/auth/client/account/address/{addressUuid}
  static Future<ApiResponse<Address>> updateAddress(
    String addressUuid,
    AddressRequest request, {
    String? authToken,
  }) async {
    if (addressUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Address UUID is required',
        statusCode: 400,
      );
    }

    // Validate request
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      return ApiResponse.error(
        message: 'Validation failed: ${validationErrors.join(', ')}',
        statusCode: 400,
      );
    }

    return await ApiService.makeRequest<Address>(
      endpoint: '/v1/auth/client/account/address/$addressUuid',
      method: 'PATCH',
      body: request.toJsonForUpdate(), // Use update-specific JSON that excludes 'type' field
      requiresAuth: true,
      authToken: authToken,
      fromJson: (json) => Address.fromJson(json),
    );
  }

  /// Get addresses with default pagination
  ///
  /// Convenience method that fetches addresses with sensible defaults:
  /// - Default limit: 20 items
  /// - Default sort: created date descending
  static Future<ApiResponse<List<Address>>> getAddressesWithDefaults({
    String? filter,
    int page = 1,
    int pageSize = 20,
    String? authToken,
  }) async {
    final offset = (page - 1) * pageSize;

    return await getAllAddresses(
      filter: filter,
      sort: '[created=DESC]',
      offset: offset,
      limit: pageSize,
      authToken: authToken,
    );
  }

  /// Search addresses by name
  ///
  /// Convenience method to search addresses by name using the filter parameter
  static Future<ApiResponse<List<Address>>> searchAddressesByName(String name, {String? authToken}) async {
    if (name.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Search name cannot be empty',
        statusCode: 400,
      );
    }

    return await getAllAddresses(
      filter: '[name[START_WITH]=$name]',
      sort: '[created=DESC]',
      authToken: authToken,
    );
  }

  /// Get addresses sorted by creation date
  ///
  /// Convenience method to get addresses sorted by creation date
  static Future<ApiResponse<List<Address>>> getAddressesByDate({
    bool ascending = false,
    int? limit,
    String? authToken,
  }) async {
    final sortOrder = ascending ? 'ASC' : 'DESC';

    return await getAllAddresses(
      sort: '[created=$sortOrder]',
      limit: limit,
      authToken: authToken,
    );
  }

  /// Validate address UUID format
  ///
  /// Basic UUID format validation
  static bool isValidUuid(String uuid) {
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    );
    return uuidRegex.hasMatch(uuid);
  }

  /// Create address request from form data
  ///
  /// Convenience method to create AddressRequest from form fields
  static AddressRequest createAddressRequest({
    required String name,
    required String phoneNumber,
    required String street,
    required String city,
    required String state,
    required String country,
    required String type,
    String? longitude,
    String? latitude,
  }) {
    // Parse phone number
    int parsedPhoneNumber;
    try {
      parsedPhoneNumber = int.parse(phoneNumber.replaceAll(RegExp(r'[^\d]'), ''));
    } catch (e) {
      parsedPhoneNumber = 0; // Will be caught by validation
    }

    // Parse coordinates
    double? parsedLongitude;
    double? parsedLatitude;

    if (longitude != null && longitude.isNotEmpty) {
      try {
        parsedLongitude = double.parse(longitude);
      } catch (e) {
        // Invalid longitude, will be caught by validation if needed
      }
    }

    if (latitude != null && latitude.isNotEmpty) {
      try {
        parsedLatitude = double.parse(latitude);
      } catch (e) {
        // Invalid latitude, will be caught by validation if needed
      }
    }

    return AddressRequest(
      name: name.trim(),
      phoneNumber: parsedPhoneNumber,
      street: street.trim(),
      city: city.trim(),
      state: state.trim(),
      country: country.trim(),
      type: type.trim(),
      longitude: parsedLongitude,
      latitude: parsedLatitude,
    );
  }

  /// Legacy method for backward compatibility - creates an address
  ///
  /// This method maintains compatibility with existing code that uses saveAddress
  /// It converts the old format to the new API format
  static Future<ApiResponse<Address>> saveAddress(Map<String, dynamic> addressData, {String? authToken}) async {
    try {
      final request = AddressRequest(
        name: addressData['addressName'] ?? addressData['name'] ?? 'Unknown',
        phoneNumber: int.tryParse(addressData['phoneNumber']?.toString() ?? '0') ?? 0,
        street: addressData['street'] ?? addressData['fullAddress'] ?? '',
        city: addressData['city'] ?? '',
        state: addressData['state'] ?? '',
        country: addressData['country'] ?? 'Nigeria',
        type: addressData['type']?.toString() ?? 'pickup',
        longitude: double.tryParse(addressData['longitude']?.toString() ?? ''),
        latitude: double.tryParse(addressData['latitude']?.toString() ?? ''),
      );

      return await createAddress(request, authToken: authToken);
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to save address: ${e.toString()}',
        statusCode: 400,
      );
    }
  }

  /// Legacy method for backward compatibility - gets all addresses
  ///
  /// This method maintains compatibility with existing code that uses getSavedAddresses
  /// It converts the new API format to the old format
  static Future<List<Map<String, dynamic>>> getSavedAddresses({String? authToken}) async {
    try {
      // Request all addresses with a high limit to ensure we get everything
      final response = await getAllAddresses(
        limit: 1000, // Set a high limit to get all addresses
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data!.map((address) => {
          'id': address.uuid,
          'addressName': address.name,
          'name': address.name,
          'phoneNumber': address.phoneNumber.toString(),
          'street': address.street,
          'city': address.city,
          'state': address.state,
          'country': address.country,
          'longitude': address.longitude.toString(),
          'latitude': address.latitude.toString(),
          'fullAddress': address.formattedAddress,
          'lastUsed': address.created.millisecondsSinceEpoch,
          'type': address.type,
        }).toList();
      }

      return [];
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AddressService: Error in getSavedAddresses: $e');
      }
      return [];
    }
  }

  /// Legacy method for backward compatibility - gets addresses by type
  ///
  /// This method maintains compatibility with existing code that uses getAddressesByType
  static Future<List<Map<String, dynamic>>> getAddressesByType(String type, {String? authToken}) async {
    final addresses = await getSavedAddresses(authToken: authToken);
    return addresses.where((addr) => addr['type'] == type).toList();
  }

  /// Legacy method for backward compatibility - marks address as recently used
  ///
  /// This method maintains compatibility with existing code that uses markAsRecentlyUsed
  /// Note: This is a no-op since the API handles usage tracking differently
  static Future<void> markAsRecentlyUsed(String addressId) async {
    // No-op: API handles usage tracking automatically
    // This method exists for backward compatibility only
  }

  /// Legacy method for backward compatibility - clears all addresses
  ///
  /// This method maintains compatibility with existing code that uses clearAllAddresses
  /// Note: This would require deleting all addresses via API calls
  static Future<void> clearAllAddresses({String? authToken}) async {
    try {
      final response = await getAllAddresses(authToken: authToken);

      if (response.success && response.data != null) {
        // Delete each address individually
        for (final address in response.data!) {
          await deleteAddress(address.uuid, authToken: authToken);
        }
      }
    } catch (e) {
      // Silently handle errors for backward compatibility
    }
  }

  // ============================================================================
  // ORDER AND PACKAGE ADDRESS MANAGEMENT METHODS
  // ============================================================================

  /// Fetch one or both pickup and destination addresses on an order
  ///
  /// [orderUuid] - A valid order UUID
  /// [addressType] - Optional address type ('pickup' or 'delivery'). If not provided, both addresses are returned
  /// Returns both pickup and delivery addresses if they exist
  static Future<ApiResponse<Map<String, Address?>>> getOrderAddresses(
    String orderUuid, {
    String? addressType,
    String? authToken,
  }) async {
    if (orderUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Order UUID is required',
        statusCode: 400,
      );
    }

    String endpoint = '/v1/auth/client/order/$orderUuid/addresses';
    if (addressType != null && addressType.isNotEmpty) {
      endpoint += '/$addressType';
    }

    try {
      final response = await ApiService.makeRequest<Map<String, Address?>>(
        endpoint: endpoint,
        method: 'GET',
        requiresAuth: true,
        authToken: authToken,
        fromJson: (json) => _parseOrderAddresses(json),
      );
      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch order addresses: $e',
        statusCode: 500,
      );
    }
  }

  /// Fetch one or both pickup and destination addresses on a package
  ///
  /// [orderUuid] - A valid order UUID
  /// [packageUuid] - A valid package UUID
  /// [addressType] - Optional address type ('pickup' or 'delivery'). If not provided, both addresses are returned
  /// Returns both pickup and delivery addresses if they exist
  static Future<ApiResponse<Map<String, Address?>>> getPackageAddresses(
    String orderUuid,
    String packageUuid, {
    String? addressType,
    String? authToken,
  }) async {
    if (orderUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Order UUID is required',
        statusCode: 400,
      );
    }

    if (packageUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Package UUID is required',
        statusCode: 400,
      );
    }

    String endpoint = '/v1/auth/client/order/$orderUuid/$packageUuid/addresses';
    if (addressType != null && addressType.isNotEmpty) {
      endpoint += '/$addressType';
    }

    try {
      final response = await ApiService.makeRequest<Map<String, Address?>>(
        endpoint: endpoint,
        method: 'GET',
        requiresAuth: true,
        authToken: authToken,
        fromJson: (json) => _parseOrderAddresses(json),
      );
      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch package addresses: $e',
        statusCode: 500,
      );
    }
  }

  /// Update an existing address on an order
  ///
  /// [orderUuid] - A valid order UUID
  /// [addressUuid] - A valid address UUID
  /// [request] - Address update request data
  static Future<ApiResponse<Address>> updateOrderAddress(
    String orderUuid,
    String addressUuid,
    AddressRequest request, {
    String? authToken,
  }) async {
    if (orderUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Order UUID is required',
        statusCode: 400,
      );
    }

    if (addressUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Address UUID is required',
        statusCode: 400,
      );
    }

    // Validate request
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      return ApiResponse.error(
        message: 'Validation failed: ${validationErrors.join(', ')}',
        statusCode: 400,
      );
    }

    return await ApiService.makeRequest<Address>(
      endpoint: '/v1/auth/client/order/$orderUuid/address/$addressUuid',
      method: 'PATCH',
      body: request.toJsonForUpdate(),
      requiresAuth: true,
      authToken: authToken,
      fromJson: (json) => Address.fromJson(json),
    );
  }

  /// Delete an existing address on an order
  ///
  /// [orderUuid] - A valid order UUID
  /// [addressUuid] - A valid address UUID
  static Future<ApiResponse<bool>> deleteOrderAddress(
    String orderUuid,
    String addressUuid, {
    String? authToken,
  }) async {
    if (orderUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Order UUID is required',
        statusCode: 400,
      );
    }

    if (addressUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Address UUID is required',
        statusCode: 400,
      );
    }

    return await ApiService.makeRequest<bool>(
      endpoint: '/v1/auth/client/order/$orderUuid/address/$addressUuid',
      method: 'DELETE',
      requiresAuth: true,
      authToken: authToken,
      fromJson: (json) => true, // Delete operations return success status
    );
  }

  /// Update an existing address on a package
  ///
  /// [orderUuid] - A valid order UUID
  /// [packageUuid] - A valid package UUID
  /// [addressUuid] - A valid address UUID
  /// [request] - Address update request data
  static Future<ApiResponse<Address>> updatePackageAddress(
    String orderUuid,
    String packageUuid,
    String addressUuid,
    AddressRequest request, {
    String? authToken,
  }) async {
    if (orderUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Order UUID is required',
        statusCode: 400,
      );
    }

    if (packageUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Package UUID is required',
        statusCode: 400,
      );
    }

    if (addressUuid.trim().isEmpty) {
      return ApiResponse.error(
        message: 'Address UUID is required',
        statusCode: 400,
      );
    }

    // Validate request
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      return ApiResponse.error(
        message: 'Validation failed: ${validationErrors.join(', ')}',
        statusCode: 400,
      );
    }

    return await ApiService.makeRequest<Address>(
      endpoint: '/v1/auth/client/order/$orderUuid/$packageUuid/address/$addressUuid',
      method: 'PATCH',
      body: request.toJsonForUpdate(),
      requiresAuth: true,
      authToken: authToken,
      fromJson: (json) => Address.fromJson(json),
    );
  }

  /// Helper method to parse order/package address response
  ///
  /// Parses the response data containing PickupAddress and DeliveryAddress
  static Map<String, Address?> _parseOrderAddresses(Map<String, dynamic> json) {
    Address? pickupAddress;
    Address? deliveryAddress;

    if (json.containsKey('PickupAddress') && json['PickupAddress'] != null) {
      pickupAddress = Address.fromJson(json['PickupAddress'] as Map<String, dynamic>);
    }

    if (json.containsKey('DeliveryAddress') && json['DeliveryAddress'] != null) {
      deliveryAddress = Address.fromJson(json['DeliveryAddress'] as Map<String, dynamic>);
    }

    return {
      'pickup': pickupAddress,
      'delivery': deliveryAddress,
    };
  }
}
